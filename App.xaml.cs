﻿< Application x: Class = "RetailManager.App"
             xmlns = "http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns: x = "http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri = "MainWindow.xaml" >
    < Application.Resources >
        < ResourceDictionary >
            < !--Button Styles-- >
            < Style x: Key = "NavButton" TargetType = "Button" >
                < Setter Property = "Background" Value = "Transparent" />
                < Setter Property = "BorderThickness" Value = "0" />
                < Setter Property = "Template" >
                    < Setter.Value >
                        < ControlTemplate TargetType = "Button" >
                            < Border Background = "{TemplateBinding Background}"
                                    BorderBrush = "{TemplateBinding BorderBrush}"
                                    BorderThickness = "{TemplateBinding BorderThickness}" >
                                < ContentPresenter HorizontalAlignment = "Left"
                                                VerticalAlignment = "Center"
                                                Margin = "15,0" />
                            </ Border >
                        </ ControlTemplate >
                    </ Setter.Value >
                </ Setter >
                < Style.Triggers >
                    < Trigger Property = "IsMouseOver" Value = "True" >
                        < Setter Property = "Background" Value = "#E8E8E8" />
                    </ Trigger >
                </ Style.Triggers >
            </ Style >

            < Style x: Key = "ActionButton" TargetType = "Button" >
                < Setter Property = "Background" Value = "#0094FF" />
                < Setter Property = "Foreground" Value = "White" />
                < Setter Property = "Padding" Value = "15,8" />
                < Setter Property = "BorderThickness" Value = "0" />
                < Setter Property = "Template" >
                    < Setter.Value >
                        < ControlTemplate TargetType = "Button" >
                            < Border Background = "{TemplateBinding Background}"
                                    CornerRadius = "4"
                                    Padding = "{TemplateBinding Padding}" >
                                < ContentPresenter HorizontalAlignment = "Center"
                                                VerticalAlignment = "Center" />
                            </ Border >
                        </ ControlTemplate >
                    </ Setter.Value >
                </ Setter >
            </ Style >

            < Style x: Key = "IconButton" TargetType = "Button" >
                < Setter Property = "Background" Value = "Transparent" />
                < Setter Property = "BorderThickness" Value = "0" />
                < Setter Property = "Padding" Value = "10" />
                < Setter Property = "Template" >
                    < Setter.Value >
                        < ControlTemplate TargetType = "Button" >
                            < Border Background = "{TemplateBinding Background}"
                                    CornerRadius = "4"
                                    Padding = "{TemplateBinding Padding}" >
                                < ContentPresenter HorizontalAlignment = "Center"
                                                VerticalAlignment = "Center" />
                            </ Border >
                        </ ControlTemplate >
                    </ Setter.Value >
                </ Setter >
                < Style.Triggers >
                    < Trigger Property = "IsMouseOver" Value = "True" >
                        < Setter Property = "Background" Value = "#F5F5F5" />
                    </ Trigger >
                </ Style.Triggers >
            </ Style >
        </ ResourceDictionary >
    </ Application.Resources >
</ Application > 