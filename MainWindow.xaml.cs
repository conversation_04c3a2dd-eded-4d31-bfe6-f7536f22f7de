﻿< Window x: Class = "RetailManager.MainWindow"
        xmlns = "http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns: x = "http://schemas.microsoft.com/winfx/2006/xaml"
        Title = "Retail Manager" Height = "768" Width = "1366"
        WindowState = "Maximized" >
    < Grid >
        < Grid.ColumnDefinitions >
            < ColumnDefinition Width = "250" />
            < ColumnDefinition Width = "*" />
        </ Grid.ColumnDefinitions >

        < !--Left Navigation Panel -->
        <StackPanel Grid.Column="0" Background="#F5F5F5">
            <Button Height="50" Margin="10,20,10,5" Style="{StaticResource NavButton}">
                <StackPanel Orientation="Horizontal">
                    <Image Source="/Assets/sales-icon.png" Width="24" Height="24"/>
                    <TextBlock Text="Sales" Margin="10,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            
            <!-- Add other navigation buttons similarly -->
            <!-- Customers, Products, Stock, Online Catalog, etc. -->
        </StackPanel>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <Grid Grid.Row="0" Background="White">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,0">
                    <Button Content="Subscribe!" Style="{StaticResource ActionButton}"/>
                    <Button Content="Settings" Style="{StaticResource IconButton}"/>
                </StackPanel>
            </Grid>

            <!-- Content Area -->
            <Frame Grid.Row="1" x:Name = "MainContent" />
        </ Grid >
    </ Grid >
</ Window > 